// 简化版Three.js核心功能 - 用于空战游戏
// 这是一个最小化的Three.js实现，包含游戏所需的基本功能

window.THREE = {};

// 数学工具
THREE.MathUtils = {
    degToRad: function(degrees) {
        return degrees * Math.PI / 180;
    },
    radToDeg: function(radians) {
        return radians * 180 / Math.PI;
    }
};

// 向量3D
THREE.Vector3 = function(x = 0, y = 0, z = 0) {
    this.x = x;
    this.y = y;
    this.z = z;
};

THREE.Vector3.prototype = {
    set: function(x, y, z) {
        this.x = x;
        this.y = y;
        this.z = z;
        return this;
    },
    
    copy: function(v) {
        this.x = v.x;
        this.y = v.y;
        this.z = v.z;
        return this;
    },
    
    clone: function() {
        return new THREE.Vector3(this.x, this.y, this.z);
    },
    
    add: function(v) {
        this.x += v.x;
        this.y += v.y;
        this.z += v.z;
        return this;
    },
    
    multiplyScalar: function(scalar) {
        this.x *= scalar;
        this.y *= scalar;
        this.z *= scalar;
        return this;
    },
    
    distanceTo: function(v) {
        const dx = this.x - v.x;
        const dy = this.y - v.y;
        const dz = this.z - v.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
};

// 颜色
THREE.Color = function(r, g, b) {
    if (typeof r === 'number' && g === undefined && b === undefined) {
        // 十六进制颜色
        this.setHex(r);
    } else {
        this.r = r || 0;
        this.g = g || 0;
        this.b = b || 0;
    }
};

THREE.Color.prototype = {
    setHex: function(hex) {
        this.r = ((hex >> 16) & 255) / 255;
        this.g = ((hex >> 8) & 255) / 255;
        this.b = (hex & 255) / 255;
        return this;
    }
};

// 场景
THREE.Scene = function() {
    this.children = [];
    this.background = null;
};

THREE.Scene.prototype = {
    add: function(object) {
        this.children.push(object);
    },
    
    remove: function(object) {
        const index = this.children.indexOf(object);
        if (index !== -1) {
            this.children.splice(index, 1);
        }
    }
};

// 相机
THREE.PerspectiveCamera = function(fov, aspect, near, far) {
    this.fov = fov;
    this.aspect = aspect;
    this.near = near;
    this.far = far;
    this.position = new THREE.Vector3();
    this.rotation = new THREE.Vector3();
};

THREE.PerspectiveCamera.prototype = {
    updateProjectionMatrix: function() {
        // 简化实现
    },
    
    lookAt: function(x, y, z) {
        if (x.isVector3) {
            // 如果传入的是Vector3对象
            // 简化实现
        }
    }
};

// 几何体基类
THREE.BufferGeometry = function() {
    this.attributes = {};
};

// 基础几何体
THREE.BoxGeometry = function(width = 1, height = 1, depth = 1) {
    THREE.BufferGeometry.call(this);
    this.width = width;
    this.height = height;
    this.depth = depth;
};

THREE.SphereGeometry = function(radius = 1, widthSegments = 8, heightSegments = 6) {
    THREE.BufferGeometry.call(this);
    this.radius = radius;
    this.widthSegments = widthSegments;
    this.heightSegments = heightSegments;
};

THREE.CylinderGeometry = function(radiusTop = 1, radiusBottom = 1, height = 1, radialSegments = 8) {
    THREE.BufferGeometry.call(this);
    this.radiusTop = radiusTop;
    this.radiusBottom = radiusBottom;
    this.height = height;
    this.radialSegments = radialSegments;
};

THREE.PlaneGeometry = function(width = 1, height = 1) {
    THREE.BufferGeometry.call(this);
    this.width = width;
    this.height = height;
};

// 材质基类
THREE.Material = function() {
    this.color = new THREE.Color(0xffffff);
    this.transparent = false;
    this.opacity = 1;
};

THREE.MeshBasicMaterial = function(parameters = {}) {
    THREE.Material.call(this);
    if (parameters.color !== undefined) this.color = new THREE.Color(parameters.color);
    if (parameters.transparent !== undefined) this.transparent = parameters.transparent;
    if (parameters.opacity !== undefined) this.opacity = parameters.opacity;
};

THREE.MeshPhongMaterial = function(parameters = {}) {
    THREE.Material.call(this);
    if (parameters.color !== undefined) this.color = new THREE.Color(parameters.color);
    if (parameters.transparent !== undefined) this.transparent = parameters.transparent;
    if (parameters.opacity !== undefined) this.opacity = parameters.opacity;
};

THREE.MeshLambertMaterial = function(parameters = {}) {
    THREE.Material.call(this);
    if (parameters.color !== undefined) this.color = new THREE.Color(parameters.color);
    if (parameters.transparent !== undefined) this.transparent = parameters.transparent;
    if (parameters.opacity !== undefined) this.opacity = parameters.opacity;
};

// 网格对象
THREE.Mesh = function(geometry, material) {
    this.geometry = geometry;
    this.material = material;
    this.position = new THREE.Vector3();
    this.rotation = new THREE.Vector3();
    this.scale = new THREE.Vector3(1, 1, 1);
    this.userData = {};
    this.castShadow = false;
    this.receiveShadow = false;
};

THREE.Mesh.prototype = {
    add: function(object) {
        if (!this.children) this.children = [];
        this.children.push(object);
    }
};

// 组对象
THREE.Group = function() {
    this.children = [];
    this.position = new THREE.Vector3();
    this.rotation = new THREE.Vector3();
    this.scale = new THREE.Vector3(1, 1, 1);
    this.userData = {};
};

THREE.Group.prototype = {
    add: function(object) {
        this.children.push(object);
    }
};

// 光照
THREE.AmbientLight = function(color, intensity = 1) {
    this.color = new THREE.Color(color);
    this.intensity = intensity;
};

THREE.DirectionalLight = function(color, intensity = 1) {
    this.color = new THREE.Color(color);
    this.intensity = intensity;
    this.position = new THREE.Vector3();
    this.castShadow = false;
    this.shadow = {
        mapSize: { width: 512, height: 512 },
        camera: {
            near: 0.5,
            far: 500,
            left: -10,
            right: 10,
            top: 10,
            bottom: -10
        }
    };
};

// 渲染器 (简化版，使用Canvas 2D)
THREE.WebGLRenderer = function(parameters = {}) {
    this.domElement = parameters.canvas || document.createElement('canvas');
    this.shadowMap = {
        enabled: false,
        type: 'PCFSoftShadowMap'
    };
    
    // 获取2D上下文用于简化渲染
    this.context = this.domElement.getContext('2d');
    
    // 简化的3D到2D投影
    this.camera = null;
    this.scene = null;
};

THREE.WebGLRenderer.prototype = {
    setSize: function(width, height) {
        this.domElement.width = width;
        this.domElement.height = height;
        this.domElement.style.width = width + 'px';
        this.domElement.style.height = height + 'px';
    },
    
    setPixelRatio: function(ratio) {
        // 简化实现
    },
    
    setAnimationLoop: function(callback) {
        const animate = () => {
            callback();
            requestAnimationFrame(animate);
        };
        animate();
    },
    
    render: function(scene, camera) {
        this.scene = scene;
        this.camera = camera;
        
        // 清除画布
        this.context.fillStyle = scene.background ? 
            `rgb(${Math.floor(scene.background.r * 255)}, ${Math.floor(scene.background.g * 255)}, ${Math.floor(scene.background.b * 255)})` : 
            '#87CEEB';
        this.context.fillRect(0, 0, this.domElement.width, this.domElement.height);
        
        // 简化渲染场景中的对象
        this.renderObjects(scene.children);
    },
    
    renderObjects: function(objects) {
        for (let obj of objects) {
            this.renderObject(obj);
        }
    },
    
    renderObject: function(obj) {
        if (obj.geometry && obj.material) {
            // 简化的3D对象渲染 - 改进投影算法
            const scale = 8; // 缩放因子
            const x = this.domElement.width / 2 + obj.position.x * scale;
            const y = this.domElement.height / 2 - obj.position.z * scale; // 使用z作为屏幕y坐标

            // 根据几何体类型确定大小
            let size = 20;
            if (obj.geometry.constructor === THREE.BoxGeometry) {
                size = Math.max(obj.geometry.width * scale, 15);
            } else if (obj.geometry.constructor === THREE.SphereGeometry) {
                size = obj.geometry.radius * scale * 2;
            }

            this.context.fillStyle = `rgb(${Math.floor(obj.material.color.r * 255)}, ${Math.floor(obj.material.color.g * 255)}, ${Math.floor(obj.material.color.b * 255)})`;

            if (obj.geometry.constructor === THREE.BoxGeometry) {
                this.context.fillRect(x - size/2, y - size/2, size, size);

                // 添加边框让飞机更明显
                this.context.strokeStyle = 'white';
                this.context.lineWidth = 2;
                this.context.strokeRect(x - size/2, y - size/2, size, size);
            } else if (obj.geometry.constructor === THREE.SphereGeometry) {
                this.context.beginPath();
                this.context.arc(x, y, size/2, 0, 2 * Math.PI);
                this.context.fill();
            }
        }

        // 渲染子对象
        if (obj.children) {
            this.renderObjects(obj.children);
        }
    }
};

// 时钟
THREE.Clock = function() {
    this.startTime = Date.now();
    this.oldTime = this.startTime;
    this.elapsedTime = 0;
    this.running = false;
};

THREE.Clock.prototype = {
    start: function() {
        this.startTime = Date.now();
        this.oldTime = this.startTime;
        this.elapsedTime = 0;
        this.running = true;
    },
    
    getDelta: function() {
        let diff = 0;
        if (this.running) {
            const newTime = Date.now();
            diff = (newTime - this.oldTime) / 1000;
            this.oldTime = newTime;
            this.elapsedTime += diff;
        }
        return diff;
    },
    
    getElapsedTime: function() {
        return this.elapsedTime;
    }
};

// 常量
THREE.PCFSoftShadowMap = 'PCFSoftShadowMap';

// 设置版本号
THREE.REVISION = '150-simplified';

console.log('✅ 简化版Three.js加载完成，版本:', THREE.REVISION);
