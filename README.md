# 🛩️ Three.js 空战游戏

一款使用Three.js开发的3D空战游戏，玩家控制战斗机击败敌机获得高分。

## 🎮 游戏特性

- **3D图形渲染**: 使用Three.js WebGL渲染引擎
- **流畅的飞机控制**: WASD键控制飞机移动，空格键发射炮弹
- **智能敌机AI**: 多种移动模式（直线、正弦波、螺旋）
- **碰撞检测系统**: 精确的炮弹与敌机碰撞检测
- **爆炸特效**: 粒子系统实现的爆炸效果
- **音效系统**: Web Audio API生成的程序化音效
- **等级系统**: 随着得分增加，游戏难度逐渐提升
- **性能优化**: 帧率显示和智能资源管理

## 🎯 游戏玩法

### 控制方式
- **W**: 向前移动
- **S**: 向后移动  
- **A**: 向左移动（飞机会倾斜）
- **D**: 向右移动（飞机会倾斜）
- **空格**: 发射炮弹
- **ESC**: 暂停/继续游戏

### 游戏目标
- 击毁敌机获得分数（每架敌机10分）
- 避免与敌机碰撞（每次碰撞失去1条生命）
- 生命值为0时游戏结束
- 挑战更高的得分和等级

## 🚀 运行游戏

### 方法1: 本地HTTP服务器
```bash
# 使用Python
python -m http.server 8000

# 或使用Node.js
npx http-server -p 8000
```

然后在浏览器中访问 `http://localhost:8000`

### 方法2: 直接打开
由于使用了ES6模块，建议使用HTTP服务器运行以避免CORS问题。

## 🛠️ 技术实现

### 核心技术栈
- **Three.js**: 3D图形渲染
- **Web Audio API**: 音效系统
- **ES6 Classes**: 面向对象架构
- **HTML5 Canvas**: 渲染目标

### 架构设计
- `GameState`: 游戏状态管理
- `InputManager`: 输入处理
- `AudioManager`: 音效管理
- `AirCombatGame`: 主游戏逻辑

### 性能优化
- 对象池管理（炮弹、敌机、爆炸效果）
- 智能粒子数量控制
- 边界检测和自动清理
- 帧率监控

## 🎨 视觉效果

- **天空背景**: 渐变天蓝色背景
- **地面**: 半透明绿色地面
- **云朵**: 随机分布的装饰性云朵
- **飞机模型**: 3D几何体组合的飞机
- **爆炸效果**: 多彩粒子爆炸动画
- **光照系统**: 环境光+方向光+阴影

## 🔧 自定义配置

游戏中的各种参数都可以在代码中轻松调整：

- 飞机速度: `player.userData.speed`
- 炮弹冷却时间: `this.bulletCooldown`
- 敌机生成频率: `spawnInterval`
- 世界边界: `this.worldBounds`

## 📱 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

需要支持WebGL和Web Audio API的现代浏览器。

## 🎵 音效说明

游戏使用Web Audio API生成程序化音效：
- **射击音效**: 高频到低频的快速扫频
- **爆炸音效**: 锯齿波低频轰鸣
- **受击音效**: 方波中频冲击音

## 🏆 未来改进

- [ ] 添加更多敌机类型
- [ ] 实现道具系统
- [ ] 添加背景音乐
- [ ] 多人对战模式
- [ ] 移动端触控支持
- [ ] 更丰富的视觉特效

## 📄 许可证

MIT License - 可自由使用和修改

---

**享受游戏！击败所有敌机，成为空战英雄！** 🏆
