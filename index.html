<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>空战游戏 - Three.js</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 100%);
            font-family: 'Arial', sans-serif;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #gameCanvas {
            display: block;
            width: 100%;
            height: 100%;
        }
        
        #gameUI {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            z-index: 100;
        }
        
        #gameUI div {
            margin-bottom: 10px;
        }
        
        #instructions {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            font-size: 14px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            z-index: 100;
        }
        
        #startScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 200;
        }
        
        #startScreen h1 {
            font-size: 48px;
            margin-bottom: 20px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.8);
        }
        
        #startScreen p {
            font-size: 18px;
            margin-bottom: 30px;
            text-align: center;
            line-height: 1.5;
        }
        
        #startButton {
            padding: 15px 30px;
            font-size: 20px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        #startButton:hover {
            background: #45a049;
        }
        
        #gameOverScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 200;
        }
        
        #gameOverScreen h1 {
            font-size: 36px;
            margin-bottom: 20px;
            color: #ff4444;
        }
        
        #finalScore {
            font-size: 24px;
            margin-bottom: 30px;
        }
        
        #restartButton {
            padding: 15px 30px;
            font-size: 20px;
            background: #2196F3;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        #restartButton:hover {
            background: #1976D2;
        }

        #pauseScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 200;
        }

        #pauseScreen h1 {
            font-size: 36px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>
        
        <!-- 游戏UI -->
        <div id="gameUI">
            <div>得分: <span id="score">0</span></div>
            <div>生命: <span id="lives">3</span></div>
            <div>敌机: <span id="enemies">0</span></div>
            <div>等级: <span id="level">1</span></div>
            <div style="font-size: 12px; opacity: 0.7;">FPS: <span id="fps">60</span></div>
        </div>
        
        <!-- 操作说明 -->
        <div id="instructions">
            <div>🎮 控制说明：</div>
            <div>WASD - 移动蓝色飞机</div>
            <div>空格 - 发射黄色炮弹</div>
            <div>ESC - 暂停游戏</div>
            <div style="margin-top: 10px; color: yellow;">
                <div>🎯 目标：击毁红色敌机</div>
                <div>⚠️ 避免碰撞敌机</div>
            </div>
        </div>
        
        <!-- 开始界面 -->
        <div id="startScreen">
            <h1>🛩️ 空战游戏</h1>
            <p>
                驾驶你的战斗机，击败所有敌机！<br>
                使用WASD控制飞机移动，空格键发射炮弹<br>
                小心不要被敌机击中！
            </p>
            <button id="startButton">开始游戏</button>
        </div>
        
        <!-- 暂停界面 -->
        <div id="pauseScreen">
            <h1>游戏暂停</h1>
            <p>按ESC键继续游戏</p>
        </div>

        <!-- 游戏结束界面 -->
        <div id="gameOverScreen">
            <h1>游戏结束</h1>
            <div id="finalScore">最终得分: 0</div>
            <button id="restartButton">重新开始</button>
        </div>
    </div>
    
    <!-- Three.js 库 - 本地简化版本 -->
    <script src="three-local.js"></script>

    <!-- 游戏主脚本 - 简化版 -->
    <script src="game-simple.js"></script>
</body>
</html>
