// 空战游戏 - Three.js 实现
// 游戏状态管理
class GameState {
    constructor() {
        this.score = 0;
        this.lives = 3;
        this.enemyCount = 0;
        this.isGameRunning = false;
        this.isPaused = false;
        this.level = 1;
    }
    
    reset() {
        this.score = 0;
        this.lives = 3;
        this.enemyCount = 0;
        this.isGameRunning = false;
        this.isPaused = false;
        this.level = 1;
    }
    
    updateUI() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('lives').textContent = this.lives;
        document.getElementById('enemies').textContent = this.enemyCount;
        document.getElementById('level').textContent = this.level;
    }
}

// 输入管理器
class InputManager {
    constructor() {
        this.keys = {
            w: false,
            a: false,
            s: false,
            d: false,
            space: false,
            escape: false
        };
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        document.addEventListener('keydown', (event) => {
            switch(event.code) {
                case 'KeyW': this.keys.w = true; break;
                case 'KeyA': this.keys.a = true; break;
                case 'KeyS': this.keys.s = true; break;
                case 'KeyD': this.keys.d = true; break;
                case 'Space': 
                    event.preventDefault();
                    this.keys.space = true; 
                    break;
                case 'Escape': this.keys.escape = true; break;
            }
        });
        
        document.addEventListener('keyup', (event) => {
            switch(event.code) {
                case 'KeyW': this.keys.w = false; break;
                case 'KeyA': this.keys.a = false; break;
                case 'KeyS': this.keys.s = false; break;
                case 'KeyD': this.keys.d = false; break;
                case 'Space': this.keys.space = false; break;
                case 'Escape': this.keys.escape = false; break;
            }
        });
    }
}

// 音效管理器
class AudioManager {
    constructor() {
        this.audioContext = null;
        this.enabled = true;
        this.init();
    }

    init() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            // 现代浏览器需要用户交互才能启动音频上下文
            if (this.audioContext.state === 'suspended') {
                console.log('音频上下文被暂停，将在用户交互时启动');
            }
        } catch (e) {
            console.warn('Web Audio API not supported:', e);
            this.enabled = false;
        }
    }

    // 启动音频上下文（需要用户交互）
    resumeAudioContext() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            this.audioContext.resume().then(() => {
                console.log('音频上下文已启动');
            }).catch(e => {
                console.warn('无法启动音频上下文:', e);
            });
        }
    }

    playShoot() {
        if (!this.enabled || !this.audioContext) return;

        try {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(200, this.audioContext.currentTime + 0.1);

        gainNode.gain.setValueAtTime(0.3, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);

            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + 0.1);
        } catch (e) {
            console.warn('播放射击音效失败:', e);
        }
    }

    playExplosion() {
        if (!this.enabled || !this.audioContext) return;

        try {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.type = 'sawtooth';
        oscillator.frequency.setValueAtTime(150, this.audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(50, this.audioContext.currentTime + 0.3);

        gainNode.gain.setValueAtTime(0.5, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.3);

            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + 0.3);
        } catch (e) {
            console.warn('播放爆炸音效失败:', e);
        }
    }

    playHit() {
        if (!this.enabled || !this.audioContext) return;

        try {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.type = 'square';
        oscillator.frequency.setValueAtTime(300, this.audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(100, this.audioContext.currentTime + 0.2);

        gainNode.gain.setValueAtTime(0.4, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.2);

            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + 0.2);
        } catch (e) {
            console.warn('播放受击音效失败:', e);
        }
    }
}

// 主游戏类
class AirCombatGame {
    constructor() {
        this.gameState = new GameState();
        this.inputManager = new InputManager();
        this.audioManager = new AudioManager();
        
        // Three.js 核心组件
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        
        // 游戏对象
        this.player = null;
        this.bullets = [];
        this.enemies = [];
        this.explosions = [];
        
        // 游戏设置
        this.worldBounds = {
            x: 50,
            y: 30,
            z: 50
        };
        
        // 时间管理
        this.clock = new THREE.Clock();
        this.lastBulletTime = 0;
        this.bulletCooldown = 0.2; // 200ms冷却时间

        // FPS计算
        this.frameCount = 0;
        this.lastFPSUpdate = 0;
        
        this.init();
    }
    
    init() {
        try {
            console.log('开始初始化Three.js...');
            this.setupThreeJS();
            console.log('Three.js设置完成');

            console.log('开始设置UI...');
            this.setupUI();
            console.log('UI设置完成');

            console.log('开始设置游戏对象...');
            this.setupGameObjects();
            console.log('游戏对象设置完成');

            console.log('开始游戏循环...');
            this.startGameLoop();
            console.log('游戏初始化完成！');
        } catch (error) {
            console.error('游戏初始化过程中出错:', error);
            alert('游戏初始化失败: ' + error.message);
        }
    }
    
    setupThreeJS() {
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB); // 天蓝色背景
        
        // 创建相机
        this.camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            1000
        );
        this.camera.position.set(0, 10, 20);
        this.camera.lookAt(0, 0, 0);
        
        // 创建渲染器
        const canvas = document.getElementById('gameCanvas');
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: canvas,
            antialias: true 
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        // 添加光照
        this.setupLighting();
        
        // 窗口大小调整
        window.addEventListener('resize', () => this.onWindowResize());
    }
    
    setupLighting() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        // 方向光（太阳光）
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(10, 20, 10);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 100;
        directionalLight.shadow.camera.left = -50;
        directionalLight.shadow.camera.right = 50;
        directionalLight.shadow.camera.top = 50;
        directionalLight.shadow.camera.bottom = -50;
        this.scene.add(directionalLight);
    }
    
    setupUI() {
        // 开始按钮事件
        document.getElementById('startButton').addEventListener('click', () => {
            this.startGame();
        });
        
        // 重新开始按钮事件
        document.getElementById('restartButton').addEventListener('click', () => {
            this.restartGame();
        });
    }
    
    setupGameObjects() {
        // 创建地面
        this.createGround();

        // 创建云朵
        this.createClouds();

        // 创建玩家飞机
        this.createPlayer();

        console.log('游戏对象设置完成');
    }

    createGround() {
        // 创建地面几何体
        const groundGeometry = new THREE.PlaneGeometry(200, 200, 50, 50);

        // 创建地面材质
        const groundMaterial = new THREE.MeshLambertMaterial({
            color: 0x90EE90,
            transparent: true,
            opacity: 0.8
        });

        // 创建地面网格
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2; // 旋转90度使其水平
        ground.position.y = -20;
        ground.receiveShadow = true;

        this.scene.add(ground);
    }

    createClouds() {
        // 创建一些装饰性的云朵
        const cloudGeometry = new THREE.SphereGeometry(3, 8, 6);
        const cloudMaterial = new THREE.MeshLambertMaterial({
            color: 0xffffff,
            transparent: true,
            opacity: 0.7
        });

        for (let i = 0; i < 20; i++) {
            const cloud = new THREE.Mesh(cloudGeometry, cloudMaterial);

            // 随机位置
            cloud.position.x = (Math.random() - 0.5) * 200;
            cloud.position.y = Math.random() * 30 + 10;
            cloud.position.z = (Math.random() - 0.5) * 200;

            // 随机缩放
            const scale = Math.random() * 2 + 0.5;
            cloud.scale.set(scale, scale * 0.5, scale);

            this.scene.add(cloud);
        }
    }

    createPlayer() {
        // 创建玩家飞机组
        this.player = new THREE.Group();

        // 机身 - 主体
        const fuselageGeometry = new THREE.CylinderGeometry(0.3, 0.8, 4, 8);
        const fuselageMaterial = new THREE.MeshPhongMaterial({ color: 0x4169E1 });
        const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
        fuselage.rotation.z = Math.PI / 2; // 旋转使其朝前
        fuselage.castShadow = true;
        this.player.add(fuselage);

        // 机翼 - 主翼
        const wingGeometry = new THREE.BoxGeometry(6, 0.2, 1.5);
        const wingMaterial = new THREE.MeshPhongMaterial({ color: 0x2E8B57 });
        const wings = new THREE.Mesh(wingGeometry, wingMaterial);
        wings.position.z = -0.5;
        wings.castShadow = true;
        this.player.add(wings);

        // 尾翼
        const tailGeometry = new THREE.BoxGeometry(2, 0.1, 0.8);
        const tailMaterial = new THREE.MeshPhongMaterial({ color: 0x2E8B57 });
        const tail = new THREE.Mesh(tailGeometry, tailMaterial);
        tail.position.x = -1.8;
        tail.position.z = -0.3;
        tail.castShadow = true;
        this.player.add(tail);

        // 垂直尾翼
        const verticalTailGeometry = new THREE.BoxGeometry(0.1, 1.5, 0.8);
        const verticalTail = new THREE.Mesh(verticalTailGeometry, tailMaterial);
        verticalTail.position.x = -1.8;
        verticalTail.position.y = 0.5;
        verticalTail.castShadow = true;
        this.player.add(verticalTail);

        // 螺旋桨
        const propellerGeometry = new THREE.BoxGeometry(0.1, 3, 0.1);
        const propellerMaterial = new THREE.MeshPhongMaterial({ color: 0x8B4513 });
        const propeller = new THREE.Mesh(propellerGeometry, propellerMaterial);
        propeller.position.x = 2.2;
        this.player.add(propeller);

        // 设置玩家初始位置
        this.player.position.set(0, 0, 0);
        this.player.userData = {
            speed: 15,
            rotationSpeed: 2,
            health: 3,
            propeller: propeller // 保存螺旋桨引用用于动画
        };

        this.scene.add(this.player);
    }

    fireBullet() {
        if (!this.player) return;

        const currentTime = this.clock.getElapsedTime();
        if (currentTime - this.lastBulletTime < this.bulletCooldown) {
            return; // 冷却时间未到
        }

        this.lastBulletTime = currentTime;

        // 创建炮弹几何体和材质
        const bulletGeometry = new THREE.SphereGeometry(0.1, 8, 6);
        const bulletMaterial = new THREE.MeshBasicMaterial({ color: 0xFFFF00 });
        const bulletMesh = new THREE.Mesh(bulletGeometry, bulletMaterial);

        // 设置炮弹初始位置（从飞机前方发射）
        bulletMesh.position.copy(this.player.position);
        bulletMesh.position.x += 2; // 从机头发射
        bulletMesh.position.y += 0.2; // 稍微向上偏移

        // 创建炮弹对象
        const bullet = {
            mesh: bulletMesh,
            velocity: new THREE.Vector3(-30, 0, 0), // 向前飞行
            life: 3.0 // 3秒生命周期
        };

        this.bullets.push(bullet);
        this.scene.add(bulletMesh);

        // 播放射击音效
        this.audioManager.playShoot();
    }

    createEnemy(x, z) {
        // 创建敌机组
        const enemy = new THREE.Group();

        // 敌机机身 - 红色
        const fuselageGeometry = new THREE.CylinderGeometry(0.25, 0.6, 3, 8);
        const fuselageMaterial = new THREE.MeshPhongMaterial({ color: 0xFF4500 });
        const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
        fuselage.rotation.z = -Math.PI / 2; // 旋转使其朝向玩家
        fuselage.castShadow = true;
        enemy.add(fuselage);

        // 敌机机翼
        const wingGeometry = new THREE.BoxGeometry(4, 0.15, 1);
        const wingMaterial = new THREE.MeshPhongMaterial({ color: 0x8B0000 });
        const wings = new THREE.Mesh(wingGeometry, wingMaterial);
        wings.position.z = -0.3;
        wings.castShadow = true;
        enemy.add(wings);

        // 敌机尾翼
        const tailGeometry = new THREE.BoxGeometry(1.5, 0.1, 0.6);
        const tail = new THREE.Mesh(tailGeometry, wingMaterial);
        tail.position.x = 1.3;
        tail.castShadow = true;
        enemy.add(tail);

        // 设置敌机位置和属性
        enemy.position.set(x, Math.random() * 5 + 2, z);
        enemy.userData = {
            speed: 8 + Math.random() * 4, // 随机速度
            health: 1,
            type: 'enemy',
            movePattern: Math.floor(Math.random() * 3), // 0: 直线, 1: 正弦波, 2: 螺旋
            time: 0,
            amplitude: Math.random() * 5 + 2 // 移动幅度
        };

        this.enemies.push(enemy);
        this.scene.add(enemy);
        this.gameState.enemyCount++;

        return enemy;
    }

    createExplosion(position) {
        // 创建爆炸效果
        const explosionGroup = new THREE.Group();

        // 创建多个粒子来模拟爆炸
        const particleCount = Math.min(8, 10 - Math.floor(this.explosions.length / 3)); // 根据现有爆炸数量调整粒子数
        for (let i = 0; i < particleCount; i++) {
            const particleGeometry = new THREE.SphereGeometry(0.2, 6, 4);
            const particleMaterial = new THREE.MeshBasicMaterial({
                color: new THREE.Color().setHSL(Math.random() * 0.1, 1, 0.5) // 红橙色
            });
            const particle = new THREE.Mesh(particleGeometry, particleMaterial);

            // 随机位置偏移
            particle.position.set(
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2
            );

            explosionGroup.add(particle);
        }

        explosionGroup.position.copy(position);

        // 爆炸对象数据
        const explosion = {
            mesh: explosionGroup,
            life: 1.0, // 1秒生命周期
            maxLife: 1.0,
            particles: explosionGroup.children
        };

        this.explosions.push(explosion);
        this.scene.add(explosionGroup);
    }
    
    startGame() {
        console.log('startGame方法被调用');
        console.log('玩家对象:', this.player);
        console.log('场景对象:', this.scene);

        // 启动音频上下文
        this.audioManager.resumeAudioContext();

        document.getElementById('startScreen').style.display = 'none';
        this.gameState.isGameRunning = true;
        this.gameState.updateUI();

        console.log('游戏状态已设置为运行中');
    }
    
    restartGame() {
        document.getElementById('gameOverScreen').style.display = 'none';
        this.gameState.reset();
        this.gameState.isGameRunning = true;
        this.gameState.updateUI();
        
        // 清理游戏对象
        this.clearGameObjects();
        this.setupGameObjects();
    }
    
    clearGameObjects() {
        // 清理所有游戏对象
        this.bullets.forEach(bullet => this.scene.remove(bullet.mesh));
        this.enemies.forEach(enemy => this.scene.remove(enemy)); // enemy本身就是Group
        this.explosions.forEach(explosion => this.scene.remove(explosion.mesh));

        this.bullets = [];
        this.enemies = [];
        this.explosions = [];

        // 重置游戏状态
        this.gameState.enemyCount = 0;
    }
    
    gameOver() {
        this.gameState.isGameRunning = false;
        document.getElementById('finalScore').textContent = `最终得分: ${this.gameState.score}`;
        document.getElementById('gameOverScreen').style.display = 'flex';
    }
    
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
    
    update(deltaTime) {
        if (!this.gameState.isGameRunning || this.gameState.isPaused) {
            return;
        }
        
        // 处理输入
        this.handleInput(deltaTime);
        
        // 更新游戏对象（将在后续任务中实现）
        this.updatePlayer(deltaTime);
        this.updateBullets(deltaTime);
        this.updateEnemies(deltaTime);
        this.updateExplosions(deltaTime);
        
        // 检测碰撞
        this.checkCollisions();
        
        // 生成敌机
        this.spawnEnemies(deltaTime);
        
        // 更新UI
        this.gameState.updateUI();

        // 更新FPS显示
        this.updateFPS();
    }
    
    handleInput(deltaTime) {
        // ESC键暂停
        if (this.inputManager.keys.escape) {
            this.gameState.isPaused = !this.gameState.isPaused;
            this.inputManager.keys.escape = false;

            // 显示/隐藏暂停界面
            const pauseScreen = document.getElementById('pauseScreen');
            pauseScreen.style.display = this.gameState.isPaused ? 'flex' : 'none';
        }

        if (!this.player) return;

        const speed = this.player.userData.speed * deltaTime;
        const rotationSpeed = this.player.userData.rotationSpeed * deltaTime;

        // WASD移动控制
        if (this.inputManager.keys.w) {
            this.player.position.z -= speed;
        }
        if (this.inputManager.keys.s) {
            this.player.position.z += speed;
        }
        if (this.inputManager.keys.a) {
            this.player.position.x -= speed;
            this.player.rotation.z = Math.min(this.player.rotation.z + rotationSpeed, Math.PI / 6);
        } else if (this.inputManager.keys.d) {
            this.player.position.x += speed;
            this.player.rotation.z = Math.max(this.player.rotation.z - rotationSpeed, -Math.PI / 6);
        } else {
            // 回正飞机倾斜
            if (this.player.rotation.z > 0) {
                this.player.rotation.z = Math.max(this.player.rotation.z - rotationSpeed * 2, 0);
            } else if (this.player.rotation.z < 0) {
                this.player.rotation.z = Math.min(this.player.rotation.z + rotationSpeed * 2, 0);
            }
        }

        // 限制玩家在边界内
        this.player.position.x = Math.max(-this.worldBounds.x, Math.min(this.worldBounds.x, this.player.position.x));
        this.player.position.z = Math.max(-this.worldBounds.z, Math.min(this.worldBounds.z, this.player.position.z));

        // 空格键发射炮弹
        if (this.inputManager.keys.space) {
            this.fireBullet();
        }
    }
    
    updatePlayer(deltaTime) {
        if (!this.player) return;

        // 螺旋桨旋转动画
        if (this.player.userData.propeller) {
            this.player.userData.propeller.rotation.x += 20 * deltaTime;
        }

        // 更新相机跟随玩家
        this.camera.position.x = this.player.position.x;
        this.camera.position.z = this.player.position.z + 20;
        this.camera.lookAt(this.player.position.x, 0, this.player.position.z - 10);
    }
    
    updateBullets(deltaTime) {
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            const bullet = this.bullets[i];

            // 更新炮弹位置
            bullet.mesh.position.add(bullet.velocity.clone().multiplyScalar(deltaTime));

            // 减少生命周期
            bullet.life -= deltaTime;

            // 检查是否超出边界或生命周期结束
            if (bullet.life <= 0 ||
                Math.abs(bullet.mesh.position.x) > this.worldBounds.x ||
                Math.abs(bullet.mesh.position.z) > this.worldBounds.z ||
                bullet.mesh.position.y < -20) {

                // 移除炮弹
                this.scene.remove(bullet.mesh);
                this.bullets.splice(i, 1);
            }
        }
    }
    
    updateEnemies(deltaTime) {
        for (let i = this.enemies.length - 1; i >= 0; i--) {
            const enemy = this.enemies[i];
            enemy.userData.time += deltaTime;

            // 根据移动模式更新位置
            switch (enemy.userData.movePattern) {
                case 0: // 直线移动
                    enemy.position.x += enemy.userData.speed * deltaTime;
                    break;

                case 1: // 正弦波移动
                    enemy.position.x += enemy.userData.speed * deltaTime;
                    enemy.position.z += Math.sin(enemy.userData.time * 2) * enemy.userData.amplitude * deltaTime;
                    break;

                case 2: // 螺旋移动
                    enemy.position.x += enemy.userData.speed * deltaTime;
                    enemy.position.y += Math.sin(enemy.userData.time * 3) * 2 * deltaTime;
                    enemy.position.z += Math.cos(enemy.userData.time * 3) * 2 * deltaTime;
                    break;
            }

            // 检查是否超出边界
            if (enemy.position.x > this.worldBounds.x + 10) {
                this.scene.remove(enemy);
                this.enemies.splice(i, 1);
                this.gameState.enemyCount--;
            }
        }
    }
    
    updateExplosions(deltaTime) {
        for (let i = this.explosions.length - 1; i >= 0; i--) {
            const explosion = this.explosions[i];

            // 减少生命周期
            explosion.life -= deltaTime;

            // 更新粒子效果
            const lifeRatio = explosion.life / explosion.maxLife;
            explosion.particles.forEach((particle, index) => {
                // 粒子扩散
                particle.position.multiplyScalar(1 + deltaTime * 3);

                // 透明度渐变
                particle.material.opacity = lifeRatio;
                particle.material.transparent = true;

                // 颜色变化
                const hue = 0.1 - (1 - lifeRatio) * 0.1; // 从红色变为黄色
                particle.material.color.setHSL(hue, 1, 0.5);
            });

            // 移除过期的爆炸效果
            if (explosion.life <= 0) {
                this.scene.remove(explosion.mesh);
                this.explosions.splice(i, 1);
            }
        }
    }
    
    checkCollisions() {
        // 检测炮弹与敌机的碰撞
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            const bullet = this.bullets[i];

            for (let j = this.enemies.length - 1; j >= 0; j--) {
                const enemy = this.enemies[j];

                // 简单的距离碰撞检测
                const distance = bullet.mesh.position.distanceTo(enemy.position);

                if (distance < 2) { // 碰撞半径
                    // 创建爆炸效果
                    this.createExplosion(enemy.position.clone());

                    // 播放爆炸音效
                    this.audioManager.playExplosion();

                    // 移除炮弹
                    this.scene.remove(bullet.mesh);
                    this.bullets.splice(i, 1);

                    // 移除敌机
                    this.scene.remove(enemy);
                    this.enemies.splice(j, 1);
                    this.gameState.enemyCount--;

                    // 增加得分
                    this.gameState.score += 10;

                    break; // 跳出内层循环
                }
            }
        }

        // 检测玩家与敌机的碰撞
        if (this.player) {
            for (let i = this.enemies.length - 1; i >= 0; i--) {
                const enemy = this.enemies[i];
                const distance = this.player.position.distanceTo(enemy.position);

                if (distance < 3) { // 玩家碰撞半径
                    // 创建爆炸效果
                    this.createExplosion(enemy.position.clone());

                    // 播放受击音效
                    this.audioManager.playHit();

                    // 移除敌机
                    this.scene.remove(enemy);
                    this.enemies.splice(i, 1);
                    this.gameState.enemyCount--;

                    // 玩家失去生命
                    this.gameState.lives--;

                    // 检查游戏是否结束
                    if (this.gameState.lives <= 0) {
                        this.gameOver();
                    }

                    break;
                }
            }
        }
    }
    
    spawnEnemies(deltaTime) {
        // 控制敌机生成频率
        if (!this.lastEnemySpawnTime) {
            this.lastEnemySpawnTime = 0;
        }

        const currentTime = this.clock.getElapsedTime();
        const spawnInterval = Math.max(1.0 - this.gameState.level * 0.1, 0.3); // 随等级增加生成频率
        const maxEnemies = 8 + this.gameState.level; // 最大敌机数量随等级增加

        if (currentTime - this.lastEnemySpawnTime > spawnInterval && this.enemies.length < maxEnemies) {
            this.lastEnemySpawnTime = currentTime;

            // 在左侧边界外生成敌机
            const spawnX = -this.worldBounds.x - 5;
            const spawnZ = (Math.random() - 0.5) * this.worldBounds.z * 1.5;

            this.createEnemy(spawnX, spawnZ);
        }

        // 检查是否需要提升等级
        if (this.gameState.score > 0 && this.gameState.score % 100 === 0 && this.gameState.score !== this.lastLevelScore) {
            this.gameState.level++;
            this.lastLevelScore = this.gameState.score;
        }
    }
    
    startGameLoop() {
        const animate = () => {
            requestAnimationFrame(animate);
            
            const deltaTime = this.clock.getDelta();
            this.update(deltaTime);
            this.renderer.render(this.scene, this.camera);
        };
        
        animate();
    }

    updateFPS() {
        this.frameCount++;
        const currentTime = this.clock.getElapsedTime();

        if (currentTime - this.lastFPSUpdate >= 1.0) { // 每秒更新一次FPS
            const fps = Math.round(this.frameCount / (currentTime - this.lastFPSUpdate));
            document.getElementById('fps').textContent = fps;
            this.frameCount = 0;
            this.lastFPSUpdate = currentTime;
        }
    }
}

// 启动游戏
window.addEventListener('DOMContentLoaded', () => {
    console.log('DOM加载完成');
    console.log('THREE对象:', typeof THREE);

    if (typeof THREE === 'undefined') {
        console.error('Three.js库未加载！');
        alert('Three.js库加载失败，请检查网络连接！');
        return;
    }

    try {
        console.log('开始创建游戏实例...');
        const game = new AirCombatGame();
        console.log('游戏实例创建成功:', game);
    } catch (error) {
        console.error('游戏初始化失败:', error);
        alert('游戏初始化失败: ' + error.message);
    }
});
