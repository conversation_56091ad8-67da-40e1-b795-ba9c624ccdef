<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化版空战游戏</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #87CEEB;
            font-family: Arial, sans-serif;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #startScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 200;
        }
        
        #startButton {
            padding: 15px 30px;
            font-size: 20px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            font-size: 16px;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div id="info">
            <div>状态: <span id="status">等待开始</span></div>
            <div>Three.js: <span id="threeStatus">检查中...</span></div>
        </div>
        
        <div id="startScreen">
            <h1>🛩️ 简化版空战游戏</h1>
            <p>点击开始测试Three.js基本功能</p>
            <button id="startButton">开始游戏</button>
        </div>
    </div>
    
    <!-- Three.js 库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r150/three.min.js"></script>
    
    <script>
        console.log('脚本开始执行');
        
        // 检查Three.js
        function checkThreeJS() {
            if (typeof THREE !== 'undefined') {
                document.getElementById('threeStatus').textContent = '✅ 已加载 v' + THREE.REVISION;
                console.log('Three.js已加载，版本:', THREE.REVISION);
                return true;
            } else {
                document.getElementById('threeStatus').textContent = '❌ 未加载';
                console.error('Three.js未加载');
                return false;
            }
        }
        
        // 简化的游戏类
        class SimpleGame {
            constructor() {
                console.log('创建SimpleGame实例');
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.cube = null;
                this.isRunning = false;
                
                this.init();
            }
            
            init() {
                try {
                    console.log('开始初始化...');
                    
                    // 创建场景
                    this.scene = new THREE.Scene();
                    this.scene.background = new THREE.Color(0x87CEEB);
                    console.log('场景创建成功');
                    
                    // 创建相机
                    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                    this.camera.position.z = 5;
                    console.log('相机创建成功');
                    
                    // 创建渲染器
                    this.renderer = new THREE.WebGLRenderer({ antialias: true });
                    this.renderer.setSize(window.innerWidth, window.innerHeight);
                    document.getElementById('gameContainer').appendChild(this.renderer.domElement);
                    console.log('渲染器创建成功');
                    
                    // 创建一个测试立方体
                    const geometry = new THREE.BoxGeometry();
                    const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
                    this.cube = new THREE.Mesh(geometry, material);
                    this.scene.add(this.cube);
                    console.log('测试立方体创建成功');
                    
                    // 设置UI事件
                    document.getElementById('startButton').addEventListener('click', () => {
                        console.log('开始按钮被点击');
                        this.startGame();
                    });
                    
                    // 开始渲染循环
                    this.animate();
                    
                    document.getElementById('status').textContent = '初始化完成';
                    console.log('游戏初始化完成');
                    
                } catch (error) {
                    console.error('初始化失败:', error);
                    document.getElementById('status').textContent = '初始化失败: ' + error.message;
                    alert('游戏初始化失败: ' + error.message);
                }
            }
            
            startGame() {
                console.log('开始游戏');
                document.getElementById('startScreen').style.display = 'none';
                this.isRunning = true;
                document.getElementById('status').textContent = '游戏运行中';
            }
            
            animate() {
                requestAnimationFrame(() => this.animate());
                
                if (this.cube) {
                    this.cube.rotation.x += 0.01;
                    this.cube.rotation.y += 0.01;
                }
                
                if (this.renderer && this.scene && this.camera) {
                    this.renderer.render(this.scene, this.camera);
                }
            }
        }
        
        // 等待DOM加载完成
        window.addEventListener('DOMContentLoaded', () => {
            console.log('DOM加载完成');
            
            if (!checkThreeJS()) {
                alert('Three.js库加载失败，请检查网络连接！');
                return;
            }
            
            try {
                const game = new SimpleGame();
                console.log('游戏实例创建成功');
            } catch (error) {
                console.error('创建游戏实例失败:', error);
                alert('创建游戏实例失败: ' + error.message);
            }
        });
        
        // 窗口大小调整
        window.addEventListener('resize', () => {
            console.log('窗口大小改变');
        });
    </script>
</body>
</html>
