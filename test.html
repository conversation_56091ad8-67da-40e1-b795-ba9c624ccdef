<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js 测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
        }
        canvas {
            display: block;
            margin: 20px auto;
            border: 1px solid #ccc;
        }
    </style>
</head>
<body>
    <h1>Three.js 库测试</h1>
    <div id="info">
        <div>Three.js 状态: <span id="threeStatus">检查中...</span></div>
        <div>WebGL 状态: <span id="webglStatus">检查中...</span></div>
    </div>
    
    <button id="testButton">测试Three.js场景</button>
    <div id="container"></div>
    
    <!-- Three.js 库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r150/three.min.js"></script>
    
    <script>
        console.log('开始测试...');
        
        // 检查Three.js是否加载
        if (typeof THREE !== 'undefined') {
            document.getElementById('threeStatus').textContent = '✅ 已加载 (版本: ' + THREE.REVISION + ')';
            console.log('Three.js版本:', THREE.REVISION);
        } else {
            document.getElementById('threeStatus').textContent = '❌ 未加载';
            console.error('Three.js未加载');
        }
        
        // 检查WebGL支持
        function checkWebGL() {
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                if (gl) {
                    document.getElementById('webglStatus').textContent = '✅ 支持';
                    return true;
                } else {
                    document.getElementById('webglStatus').textContent = '❌ 不支持';
                    return false;
                }
            } catch (e) {
                document.getElementById('webglStatus').textContent = '❌ 错误: ' + e.message;
                return false;
            }
        }
        
        checkWebGL();
        
        // 测试按钮
        document.getElementById('testButton').addEventListener('click', function() {
            console.log('测试按钮被点击');
            
            if (typeof THREE === 'undefined') {
                alert('Three.js未加载，无法创建场景');
                return;
            }
            
            try {
                // 创建简单的Three.js场景
                const scene = new THREE.Scene();
                const camera = new THREE.PerspectiveCamera(75, 800/600, 0.1, 1000);
                const renderer = new THREE.WebGLRenderer();
                
                renderer.setSize(800, 600);
                renderer.setClearColor(0x87CEEB);
                
                // 清除之前的canvas
                const container = document.getElementById('container');
                container.innerHTML = '';
                container.appendChild(renderer.domElement);
                
                // 创建一个简单的立方体
                const geometry = new THREE.BoxGeometry();
                const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
                const cube = new THREE.Mesh(geometry, material);
                scene.add(cube);
                
                camera.position.z = 5;
                
                // 渲染场景
                function animate() {
                    requestAnimationFrame(animate);
                    cube.rotation.x += 0.01;
                    cube.rotation.y += 0.01;
                    renderer.render(scene, camera);
                }
                
                animate();
                
                console.log('Three.js场景创建成功！');
                alert('Three.js测试成功！你应该能看到一个旋转的绿色立方体。');
                
            } catch (error) {
                console.error('Three.js测试失败:', error);
                alert('Three.js测试失败: ' + error.message);
            }
        });
    </script>
</body>
</html>
