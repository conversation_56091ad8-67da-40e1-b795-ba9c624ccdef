<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D空战游戏 - Three.js</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 100%);
            font-family: 'Arial', sans-serif;
        }
        
        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #gameCanvas {
            display: block;
            width: 100%;
            height: 100%;
        }
        
        #gameUI {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            z-index: 100;
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 10px;
        }
        
        #gameUI div {
            margin-bottom: 8px;
        }
        
        #instructions {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            font-size: 14px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            z-index: 100;
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 10px;
        }
        
        #startScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 200;
        }
        
        #startScreen h1 {
            font-size: 48px;
            margin-bottom: 20px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.8);
            background: linear-gradient(45deg, #FFD700, #FF6347);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        #startScreen p {
            font-size: 18px;
            margin-bottom: 30px;
            text-align: center;
            line-height: 1.5;
            max-width: 600px;
        }
        
        #startButton {
            padding: 15px 30px;
            font-size: 20px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        
        #startButton:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.4);
        }
        
        #gameOverScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 200;
        }
        
        #gameOverScreen h1 {
            font-size: 36px;
            margin-bottom: 20px;
            color: #ff4444;
        }
        
        #finalScore {
            font-size: 24px;
            margin-bottom: 30px;
        }
        
        #restartButton {
            padding: 15px 30px;
            font-size: 20px;
            background: linear-gradient(45deg, #2196F3, #1976D2);
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        
        #restartButton:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.4);
        }
        
        #pauseScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 200;
        }
        
        #pauseScreen h1 {
            font-size: 36px;
            margin-bottom: 20px;
        }
        
        #loadingScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 300;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas"></canvas>
        
        <!-- 加载界面 -->
        <div id="loadingScreen">
            <div class="spinner"></div>
            <h2>正在加载3D空战游戏...</h2>
            <p id="loadingStatus">正在加载Three.js库...</p>
        </div>
        
        <!-- 游戏UI -->
        <div id="gameUI" style="display: none;">
            <div>🎯 得分: <span id="score">0</span></div>
            <div>❤️ 生命: <span id="lives">3</span></div>
            <div>🛩️ 敌机: <span id="enemies">0</span></div>
            <div>⭐ 等级: <span id="level">1</span></div>
            <div style="font-size: 12px; opacity: 0.7;">FPS: <span id="fps">60</span></div>
        </div>
        
        <!-- 操作说明 -->
        <div id="instructions" style="display: none;">
            <div>🎮 <strong>3D空战控制</strong></div>
            <div>WASD - 飞机移动</div>
            <div>鼠标 - 视角控制</div>
            <div>空格 - 发射导弹</div>
            <div>Shift - 加速飞行</div>
            <div>ESC - 暂停游戏</div>
            <div style="margin-top: 10px; color: #FFD700;">
                <div>🎯 击毁敌机获得分数</div>
                <div>⚠️ 避免碰撞和敌火</div>
            </div>
        </div>
        
        <!-- 开始界面 -->
        <div id="startScreen" style="display: none;">
            <h1>🛩️ 3D空战游戏</h1>
            <p>
                驾驶最先进的战斗机，在3D空域中与敌机激战！<br>
                体验真实的飞行物理和华丽的3D特效<br>
                使用WASD控制飞机，鼠标控制视角，空格发射导弹<br>
                准备好成为空战王牌了吗？
            </p>
            <button id="startButton">🚀 开始3D空战</button>
        </div>
        
        <!-- 暂停界面 -->
        <div id="pauseScreen">
            <h1>⏸️ 游戏暂停</h1>
            <p>按ESC键继续3D空战</p>
        </div>
        
        <!-- 游戏结束界面 -->
        <div id="gameOverScreen">
            <h1>💥 战斗结束</h1>
            <div id="finalScore">最终得分: 0</div>
            <button id="restartButton">🔄 重新开始</button>
        </div>
    </div>
    
    <!-- Three.js 库 - 使用ES6模块 -->
    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.158.0/build/three.module.js",
            "three/addons/": "https://unpkg.com/three@0.158.0/examples/jsm/"
        }
    }
    </script>
    
    <!-- 游戏主脚本 -->
    <script type="module" src="game-3d.js"></script>
</body>
</html>
