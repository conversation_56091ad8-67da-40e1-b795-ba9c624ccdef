// 简化版空战游戏 - 适配简化版Three.js
// 游戏状态管理
class GameState {
    constructor() {
        this.score = 0;
        this.lives = 3;
        this.enemyCount = 0;
        this.isGameRunning = false;
        this.isPaused = false;
        this.level = 1;
    }
    
    reset() {
        this.score = 0;
        this.lives = 3;
        this.enemyCount = 0;
        this.isGameRunning = false;
        this.isPaused = false;
        this.level = 1;
    }
    
    updateUI() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('lives').textContent = this.lives;
        document.getElementById('enemies').textContent = this.enemyCount;
        document.getElementById('level').textContent = this.level;
        document.getElementById('fps').textContent = '60';
    }
}

// 输入管理器
class InputManager {
    constructor() {
        this.keys = {
            w: false,
            a: false,
            s: false,
            d: false,
            space: false,
            escape: false
        };
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        document.addEventListener('keydown', (event) => {
            switch(event.code) {
                case 'KeyW': this.keys.w = true; break;
                case 'KeyA': this.keys.a = true; break;
                case 'KeyS': this.keys.s = true; break;
                case 'KeyD': this.keys.d = true; break;
                case 'Space': 
                    event.preventDefault();
                    this.keys.space = true; 
                    break;
                case 'Escape': this.keys.escape = true; break;
            }
        });
        
        document.addEventListener('keyup', (event) => {
            switch(event.code) {
                case 'KeyW': this.keys.w = false; break;
                case 'KeyA': this.keys.a = false; break;
                case 'KeyS': this.keys.s = false; break;
                case 'KeyD': this.keys.d = false; break;
                case 'Space': this.keys.space = false; break;
                case 'Escape': this.keys.escape = false; break;
            }
        });
    }
}

// 简化版空战游戏
class SimpleAirCombatGame {
    constructor() {
        this.gameState = new GameState();
        this.inputManager = new InputManager();
        
        // Three.js 核心组件
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        
        // 游戏对象
        this.player = null;
        this.bullets = [];
        this.enemies = [];
        
        // 时间管理
        this.clock = new THREE.Clock();
        this.lastBulletTime = 0;
        this.bulletCooldown = 0.3;
        this.lastEnemySpawnTime = 0;
        
        this.init();
    }
    
    init() {
        try {
            console.log('开始初始化游戏...');
            this.setupThreeJS();
            this.setupUI();
            this.setupGameObjects();
            this.startGameLoop();
            console.log('游戏初始化完成！');
        } catch (error) {
            console.error('游戏初始化失败:', error);
            alert('游戏初始化失败: ' + error.message);
        }
    }
    
    setupThreeJS() {
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87CEEB);
        
        // 创建相机
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.camera.position.set(0, 10, 20);
        
        // 创建渲染器
        const canvas = document.getElementById('gameCanvas');
        this.renderer = new THREE.WebGLRenderer({ canvas: canvas });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        
        // 添加光照
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(10, 20, 10);
        this.scene.add(directionalLight);
        
        // 窗口大小调整
        window.addEventListener('resize', () => this.onWindowResize());
    }
    
    setupUI() {
        // 开始按钮事件
        document.getElementById('startButton').addEventListener('click', () => {
            this.startGame();
        });
        
        // 重新开始按钮事件
        document.getElementById('restartButton').addEventListener('click', () => {
            this.restartGame();
        });
    }
    
    setupGameObjects() {
        // 创建地面
        const groundGeometry = new THREE.PlaneGeometry(200, 200);
        const groundMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x90EE90,
            transparent: true,
            opacity: 0.8
        });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.position.y = -20;
        this.scene.add(ground);
        
        // 创建玩家飞机
        this.createPlayer();
    }
    
    createPlayer() {
        // 创建简化的玩家飞机
        const playerGeometry = new THREE.BoxGeometry(4, 1, 2);
        const playerMaterial = new THREE.MeshPhongMaterial({ color: 0x4169E1 });
        this.player = new THREE.Mesh(playerGeometry, playerMaterial);
        this.player.position.set(0, 0, 0);
        this.player.userData = {
            speed: 15,
            health: 3
        };
        this.scene.add(this.player);
    }
    
    createEnemy() {
        const enemyGeometry = new THREE.BoxGeometry(3, 1, 1.5);
        const enemyMaterial = new THREE.MeshPhongMaterial({ color: 0xFF4500 });
        const enemy = new THREE.Mesh(enemyGeometry, enemyMaterial);

        // 从右侧生成敌机，向左移动
        enemy.position.set(50, Math.random() * 5, (Math.random() - 0.5) * 40);
        enemy.userData = {
            speed: -(8 + Math.random() * 4), // 负速度，向左移动
            health: 1
        };

        this.enemies.push(enemy);
        this.scene.add(enemy);
        this.gameState.enemyCount++;
    }
    
    fireBullet() {
        if (!this.player) return;
        
        const currentTime = this.clock.getElapsedTime();
        if (currentTime - this.lastBulletTime < this.bulletCooldown) {
            return;
        }
        
        this.lastBulletTime = currentTime;
        
        const bulletGeometry = new THREE.SphereGeometry(0.2);
        const bulletMaterial = new THREE.MeshBasicMaterial({ color: 0xFFFF00 });
        const bulletMesh = new THREE.Mesh(bulletGeometry, bulletMaterial);
        
        bulletMesh.position.copy(this.player.position);
        bulletMesh.position.x += 2;
        
        const bullet = {
            mesh: bulletMesh,
            velocity: new THREE.Vector3(30, 0, 0), // 向右发射
            life: 3.0
        };
        
        this.bullets.push(bullet);
        this.scene.add(bulletMesh);
    }
    
    startGame() {
        console.log('开始游戏');
        document.getElementById('startScreen').style.display = 'none';
        this.gameState.isGameRunning = true;
        this.gameState.updateUI();
        this.clock.start();

        // 显示控制提示
        setTimeout(() => {
            console.log('游戏提示：');
            console.log('- 蓝色方块是你的飞机');
            console.log('- 红色方块是敌机');
            console.log('- 使用WASD键移动飞机');
            console.log('- 按空格键发射黄色炮弹');
            console.log('- 击中敌机得分，碰到敌机失去生命');
        }, 1000);
    }
    
    restartGame() {
        document.getElementById('gameOverScreen').style.display = 'none';
        this.gameState.reset();
        this.gameState.isGameRunning = true;
        this.gameState.updateUI();
        
        // 清理游戏对象
        this.clearGameObjects();
        this.setupGameObjects();
    }
    
    clearGameObjects() {
        this.bullets.forEach(bullet => this.scene.remove(bullet.mesh));
        this.enemies.forEach(enemy => this.scene.remove(enemy));
        this.bullets = [];
        this.enemies = [];
        this.gameState.enemyCount = 0;
    }
    
    gameOver() {
        this.gameState.isGameRunning = false;
        document.getElementById('finalScore').textContent = `最终得分: ${this.gameState.score}`;
        document.getElementById('gameOverScreen').style.display = 'flex';
    }
    
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
    
    update(deltaTime) {
        if (!this.gameState.isGameRunning || this.gameState.isPaused) {
            return;
        }
        
        this.handleInput(deltaTime);
        this.updatePlayer(deltaTime);
        this.updateBullets(deltaTime);
        this.updateEnemies(deltaTime);
        this.checkCollisions();
        this.spawnEnemies();
        this.gameState.updateUI();
    }
    
    handleInput(deltaTime) {
        // ESC键暂停
        if (this.inputManager.keys.escape) {
            this.gameState.isPaused = !this.gameState.isPaused;
            this.inputManager.keys.escape = false;
            
            const pauseScreen = document.getElementById('pauseScreen');
            pauseScreen.style.display = this.gameState.isPaused ? 'flex' : 'none';
        }
        
        if (!this.player) return;
        
        const speed = this.player.userData.speed * deltaTime;
        
        // WASD移动控制
        if (this.inputManager.keys.w) {
            this.player.position.z -= speed;
        }
        if (this.inputManager.keys.s) {
            this.player.position.z += speed;
        }
        if (this.inputManager.keys.a) {
            this.player.position.x -= speed;
        }
        if (this.inputManager.keys.d) {
            this.player.position.x += speed;
        }
        
        // 限制玩家在边界内
        this.player.position.x = Math.max(-40, Math.min(40, this.player.position.x));
        this.player.position.z = Math.max(-40, Math.min(40, this.player.position.z));
        
        // 空格键发射炮弹
        if (this.inputManager.keys.space) {
            this.fireBullet();
        }
    }
    
    updatePlayer(deltaTime) {
        if (!this.player) return;

        // 更新相机跟随玩家（固定相机位置，让玩家在屏幕中央移动）
        // 不移动相机，让玩家在固定视野内移动
    }
    
    updateBullets(deltaTime) {
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            const bullet = this.bullets[i];
            
            bullet.mesh.position.add(bullet.velocity.clone().multiplyScalar(deltaTime));
            bullet.life -= deltaTime;
            
            if (bullet.life <= 0 || Math.abs(bullet.mesh.position.x) > 60) {
                this.scene.remove(bullet.mesh);
                this.bullets.splice(i, 1);
            }
        }
    }
    
    updateEnemies(deltaTime) {
        for (let i = this.enemies.length - 1; i >= 0; i--) {
            const enemy = this.enemies[i];
            enemy.position.x += enemy.userData.speed * deltaTime;

            // 敌机移动到左侧边界外时移除
            if (enemy.position.x < -60) {
                this.scene.remove(enemy);
                this.enemies.splice(i, 1);
                this.gameState.enemyCount--;
            }
        }
    }
    
    checkCollisions() {
        // 检测炮弹与敌机的碰撞
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            const bullet = this.bullets[i];
            
            for (let j = this.enemies.length - 1; j >= 0; j--) {
                const enemy = this.enemies[j];
                
                const distance = bullet.mesh.position.distanceTo(enemy.position);
                
                if (distance < 3) {
                    // 移除炮弹和敌机
                    this.scene.remove(bullet.mesh);
                    this.bullets.splice(i, 1);
                    
                    this.scene.remove(enemy);
                    this.enemies.splice(j, 1);
                    this.gameState.enemyCount--;
                    
                    // 增加得分
                    this.gameState.score += 10;
                    
                    break;
                }
            }
        }
        
        // 检测玩家与敌机的碰撞
        if (this.player) {
            for (let i = this.enemies.length - 1; i >= 0; i--) {
                const enemy = this.enemies[i];
                const distance = this.player.position.distanceTo(enemy.position);
                
                if (distance < 4) {
                    this.scene.remove(enemy);
                    this.enemies.splice(i, 1);
                    this.gameState.enemyCount--;
                    
                    this.gameState.lives--;
                    
                    if (this.gameState.lives <= 0) {
                        this.gameOver();
                    }
                    
                    break;
                }
            }
        }
    }
    
    spawnEnemies() {
        const currentTime = this.clock.getElapsedTime();
        const spawnInterval = Math.max(2.0 - this.gameState.level * 0.2, 0.5);
        
        if (currentTime - this.lastEnemySpawnTime > spawnInterval && this.enemies.length < 5) {
            this.lastEnemySpawnTime = currentTime;
            this.createEnemy();
        }
        
        // 检查是否需要提升等级
        if (this.gameState.score > 0 && this.gameState.score % 50 === 0 && this.gameState.score !== this.lastLevelScore) {
            this.gameState.level++;
            this.lastLevelScore = this.gameState.score;
        }
    }
    
    startGameLoop() {
        this.renderer.setAnimationLoop(() => {
            const deltaTime = this.clock.getDelta();
            this.update(deltaTime);
            this.renderer.render(this.scene, this.camera);
        });
    }
}

// 启动游戏
window.addEventListener('DOMContentLoaded', () => {
    console.log('DOM加载完成，启动简化版游戏');
    
    if (typeof THREE === 'undefined') {
        console.error('Three.js未加载');
        alert('Three.js库未加载，请刷新页面重试！');
        return;
    }
    
    try {
        const game = new SimpleAirCombatGame();
        console.log('简化版游戏启动成功');
    } catch (error) {
        console.error('游戏启动失败:', error);
        alert('游戏启动失败: ' + error.message);
    }
});
