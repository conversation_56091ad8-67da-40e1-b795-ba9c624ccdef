// 3D空战游戏 - 使用最新Three.js
import * as THREE from 'three';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';

// 游戏状态管理
class GameState {
    constructor() {
        this.score = 0;
        this.lives = 3;
        this.enemyCount = 0;
        this.isGameRunning = false;
        this.isPaused = false;
        this.level = 1;
        this.isLoaded = false;
    }
    
    reset() {
        this.score = 0;
        this.lives = 3;
        this.enemyCount = 0;
        this.isGameRunning = false;
        this.isPaused = false;
        this.level = 1;
    }
    
    updateUI() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('lives').textContent = this.lives;
        document.getElementById('enemies').textContent = this.enemyCount;
        document.getElementById('level').textContent = this.level;
    }
}

// 输入管理器
class InputManager {
    constructor() {
        this.keys = {
            w: false, a: false, s: false, d: false,
            space: false, escape: false, shift: false
        };
        this.mouse = { x: 0, y: 0, deltaX: 0, deltaY: 0 };
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', (event) => {
            switch(event.code) {
                case 'KeyW': this.keys.w = true; break;
                case 'KeyA': this.keys.a = true; break;
                case 'KeyS': this.keys.s = true; break;
                case 'KeyD': this.keys.d = true; break;
                case 'Space': 
                    event.preventDefault();
                    this.keys.space = true; 
                    break;
                case 'Escape': this.keys.escape = true; break;
                case 'ShiftLeft': 
                case 'ShiftRight': this.keys.shift = true; break;
            }
        });
        
        document.addEventListener('keyup', (event) => {
            switch(event.code) {
                case 'KeyW': this.keys.w = false; break;
                case 'KeyA': this.keys.a = false; break;
                case 'KeyS': this.keys.s = false; break;
                case 'KeyD': this.keys.d = false; break;
                case 'Space': this.keys.space = false; break;
                case 'Escape': this.keys.escape = false; break;
                case 'ShiftLeft': 
                case 'ShiftRight': this.keys.shift = false; break;
            }
        });
        
        // 鼠标事件
        document.addEventListener('mousemove', (event) => {
            this.mouse.deltaX = event.movementX || 0;
            this.mouse.deltaY = event.movementY || 0;
            this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        });
    }
}

// 音效管理器
class AudioManager {
    constructor() {
        this.audioContext = null;
        this.enabled = true;
        this.init();
    }
    
    init() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.warn('Web Audio API not supported:', e);
            this.enabled = false;
        }
    }
    
    resumeAudioContext() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            this.audioContext.resume().then(() => {
                console.log('音频上下文已启动');
            }).catch(e => {
                console.warn('无法启动音频上下文:', e);
            });
        }
    }
    
    playShoot() {
        if (!this.enabled || !this.audioContext) return;
        
        try {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);
            
            oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(200, this.audioContext.currentTime + 0.1);
            
            gainNode.gain.setValueAtTime(0.3, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);
            
            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + 0.1);
        } catch (e) {
            console.warn('播放射击音效失败:', e);
        }
    }
    
    playExplosion() {
        if (!this.enabled || !this.audioContext) return;
        
        try {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);
            
            oscillator.type = 'sawtooth';
            oscillator.frequency.setValueAtTime(150, this.audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(50, this.audioContext.currentTime + 0.3);
            
            gainNode.gain.setValueAtTime(0.5, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.3);
            
            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + 0.3);
        } catch (e) {
            console.warn('播放爆炸音效失败:', e);
        }
    }
    
    playHit() {
        if (!this.enabled || !this.audioContext) return;
        
        try {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);
            
            oscillator.type = 'square';
            oscillator.frequency.setValueAtTime(300, this.audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(100, this.audioContext.currentTime + 0.2);
            
            gainNode.gain.setValueAtTime(0.4, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.2);
            
            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + 0.2);
        } catch (e) {
            console.warn('播放受击音效失败:', e);
        }
    }
}

// 主游戏类
class AirCombat3D {
    constructor() {
        this.gameState = new GameState();
        this.inputManager = new InputManager();
        this.audioManager = new AudioManager();
        
        // Three.js 核心组件
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        
        // 游戏对象
        this.player = null;
        this.missiles = [];
        this.enemies = [];
        this.explosions = [];
        this.particles = [];
        
        // 游戏设置
        this.worldBounds = { x: 200, y: 100, z: 200 };
        
        // 时间管理
        this.clock = new THREE.Clock();
        this.lastMissileTime = 0;
        this.missileCooldown = 0.2;
        this.lastEnemySpawnTime = 0;
        
        // FPS计算
        this.frameCount = 0;
        this.lastFPSUpdate = 0;
        
        this.init();
    }
    
    async init() {
        try {
            console.log('开始初始化3D空战游戏...');
            
            // 更新加载状态
            document.getElementById('loadingStatus').textContent = '正在初始化3D引擎...';
            
            await this.setupThreeJS();
            document.getElementById('loadingStatus').textContent = '正在创建3D场景...';
            
            await this.setupScene();
            document.getElementById('loadingStatus').textContent = '正在加载游戏对象...';
            
            await this.setupGameObjects();
            document.getElementById('loadingStatus').textContent = '正在设置用户界面...';
            
            this.setupUI();
            document.getElementById('loadingStatus').textContent = '准备就绪！';
            
            // 隐藏加载界面，显示开始界面
            setTimeout(() => {
                document.getElementById('loadingScreen').style.display = 'none';
                document.getElementById('startScreen').style.display = 'flex';
                document.getElementById('gameUI').style.display = 'block';
                document.getElementById('instructions').style.display = 'block';
                this.gameState.isLoaded = true;
            }, 500);
            
            this.startGameLoop();
            console.log('3D空战游戏初始化完成！');
            
        } catch (error) {
            console.error('游戏初始化失败:', error);
            document.getElementById('loadingStatus').textContent = '加载失败: ' + error.message;
            alert('游戏初始化失败: ' + error.message);
        }
    }
    
    async setupThreeJS() {
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.fog = new THREE.Fog(0x87CEEB, 50, 500);
        
        // 创建相机
        this.camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            1000
        );
        this.camera.position.set(0, 20, 50);
        
        // 创建渲染器
        const canvas = document.getElementById('gameCanvas');
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: canvas,
            antialias: true,
            alpha: true
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(window.devicePixelRatio);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputColorSpace = THREE.SRGBColorSpace;
        
        // 设置背景渐变
        this.scene.background = new THREE.Color(0x87CEEB);
        
        // 窗口大小调整
        window.addEventListener('resize', () => this.onWindowResize());
    }
    
    async setupScene() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);
        
        // 主方向光（太阳）
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(100, 100, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        directionalLight.shadow.camera.left = -100;
        directionalLight.shadow.camera.right = 100;
        directionalLight.shadow.camera.top = 100;
        directionalLight.shadow.camera.bottom = -100;
        this.scene.add(directionalLight);
        
        // 创建天空盒
        await this.createSkybox();
        
        // 创建地面
        this.createTerrain();
        
        // 创建云朵
        this.createClouds();
    }

    async createSkybox() {
        // 创建简单的渐变天空
        const skyGeometry = new THREE.SphereGeometry(400, 32, 32);
        const skyMaterial = new THREE.ShaderMaterial({
            uniforms: {
                topColor: { value: new THREE.Color(0x0077ff) },
                bottomColor: { value: new THREE.Color(0xffffff) },
                offset: { value: 33 },
                exponent: { value: 0.6 }
            },
            vertexShader: `
                varying vec3 vWorldPosition;
                void main() {
                    vec4 worldPosition = modelMatrix * vec4(position, 1.0);
                    vWorldPosition = worldPosition.xyz;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform vec3 topColor;
                uniform vec3 bottomColor;
                uniform float offset;
                uniform float exponent;
                varying vec3 vWorldPosition;
                void main() {
                    float h = normalize(vWorldPosition + offset).y;
                    gl_FragColor = vec4(mix(bottomColor, topColor, max(pow(max(h, 0.0), exponent), 0.0)), 1.0);
                }
            `,
            side: THREE.BackSide
        });

        const sky = new THREE.Mesh(skyGeometry, skyMaterial);
        this.scene.add(sky);
    }

    createTerrain() {
        // 创建地形
        const terrainGeometry = new THREE.PlaneGeometry(500, 500, 50, 50);

        // 添加高度变化
        const vertices = terrainGeometry.attributes.position.array;
        for (let i = 0; i < vertices.length; i += 3) {
            vertices[i + 2] = Math.random() * 10 - 5; // z坐标（高度）
        }
        terrainGeometry.attributes.position.needsUpdate = true;
        terrainGeometry.computeVertexNormals();

        const terrainMaterial = new THREE.MeshLambertMaterial({
            color: 0x90EE90,
            wireframe: false
        });

        const terrain = new THREE.Mesh(terrainGeometry, terrainMaterial);
        terrain.rotation.x = -Math.PI / 2;
        terrain.position.y = -50;
        terrain.receiveShadow = true;
        this.scene.add(terrain);
    }

    createClouds() {
        // 创建3D云朵
        const cloudGroup = new THREE.Group();

        for (let i = 0; i < 30; i++) {
            const cloudGeometry = new THREE.SphereGeometry(
                Math.random() * 8 + 5,
                8,
                6
            );
            const cloudMaterial = new THREE.MeshLambertMaterial({
                color: 0xffffff,
                transparent: true,
                opacity: 0.6
            });

            const cloud = new THREE.Mesh(cloudGeometry, cloudMaterial);

            // 随机位置
            cloud.position.x = (Math.random() - 0.5) * 400;
            cloud.position.y = Math.random() * 50 + 30;
            cloud.position.z = (Math.random() - 0.5) * 400;

            // 随机缩放
            const scale = Math.random() * 2 + 0.5;
            cloud.scale.set(scale, scale * 0.5, scale);

            cloudGroup.add(cloud);
        }

        this.scene.add(cloudGroup);
        this.cloudGroup = cloudGroup;
    }

    async setupGameObjects() {
        // 创建玩家飞机
        this.createPlayer();

        // 设置相机控制
        this.setupCameraControls();
    }

    createPlayer() {
        // 创建3D战斗机
        this.player = new THREE.Group();

        // 机身
        const fuselageGeometry = new THREE.CylinderGeometry(0.5, 1.2, 8, 8);
        const fuselageMaterial = new THREE.MeshPhongMaterial({
            color: 0x4169E1,
            shininess: 100
        });
        const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
        fuselage.rotation.z = Math.PI / 2;
        fuselage.castShadow = true;
        this.player.add(fuselage);

        // 主翼
        const wingGeometry = new THREE.BoxGeometry(12, 0.3, 2.5);
        const wingMaterial = new THREE.MeshPhongMaterial({
            color: 0x2E8B57,
            shininess: 80
        });
        const wings = new THREE.Mesh(wingGeometry, wingMaterial);
        wings.position.z = -1;
        wings.castShadow = true;
        this.player.add(wings);

        // 尾翼
        const tailGeometry = new THREE.BoxGeometry(3, 0.2, 1.5);
        const tail = new THREE.Mesh(tailGeometry, wingMaterial);
        tail.position.x = -3;
        tail.position.z = -0.5;
        tail.castShadow = true;
        this.player.add(tail);

        // 垂直尾翼
        const verticalTailGeometry = new THREE.BoxGeometry(0.2, 3, 1.5);
        const verticalTail = new THREE.Mesh(verticalTailGeometry, wingMaterial);
        verticalTail.position.x = -3;
        verticalTail.position.y = 1;
        verticalTail.castShadow = true;
        this.player.add(verticalTail);

        // 引擎喷口
        const engineGeometry = new THREE.CylinderGeometry(0.3, 0.5, 1, 8);
        const engineMaterial = new THREE.MeshPhongMaterial({
            color: 0x8B4513,
            emissive: 0x330000
        });
        const engine1 = new THREE.Mesh(engineGeometry, engineMaterial);
        engine1.rotation.z = Math.PI / 2;
        engine1.position.x = -4;
        engine1.position.z = -1;
        this.player.add(engine1);

        const engine2 = new THREE.Mesh(engineGeometry, engineMaterial);
        engine2.rotation.z = Math.PI / 2;
        engine2.position.x = -4;
        engine2.position.z = 1;
        this.player.add(engine2);

        // 设置玩家属性
        this.player.position.set(0, 10, 0);
        this.player.userData = {
            speed: 30,
            rotationSpeed: 2,
            health: 3,
            velocity: new THREE.Vector3(),
            acceleration: new THREE.Vector3()
        };

        this.scene.add(this.player);
    }

    setupCameraControls() {
        // 设置轨道控制器（可选）
        this.controls = new OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.enableZoom = true;
        this.controls.enablePan = false;
        this.controls.maxPolarAngle = Math.PI;
        this.controls.minDistance = 10;
        this.controls.maxDistance = 100;

        // 设置相机跟随玩家
        this.controls.target.copy(this.player.position);
    }

    setupUI() {
        // 开始按钮事件
        document.getElementById('startButton').addEventListener('click', () => {
            this.startGame();
        });

        // 重新开始按钮事件
        document.getElementById('restartButton').addEventListener('click', () => {
            this.restartGame();
        });

        // 指针锁定（用于鼠标控制）
        this.setupPointerLock();
    }

    setupPointerLock() {
        const canvas = this.renderer.domElement;

        canvas.addEventListener('click', () => {
            if (this.gameState.isGameRunning && !this.gameState.isPaused) {
                canvas.requestPointerLock();
            }
        });

        document.addEventListener('pointerlockchange', () => {
            if (document.pointerLockElement === canvas) {
                console.log('指针锁定激活 - 鼠标控制飞机');
            } else {
                console.log('指针锁定取消');
            }
        });
    }

    createEnemy() {
        // 创建3D敌机
        const enemy = new THREE.Group();

        // 敌机机身
        const fuselageGeometry = new THREE.CylinderGeometry(0.4, 0.8, 6, 8);
        const fuselageMaterial = new THREE.MeshPhongMaterial({
            color: 0xFF4500,
            shininess: 100
        });
        const fuselage = new THREE.Mesh(fuselageGeometry, fuselageMaterial);
        fuselage.rotation.z = -Math.PI / 2;
        fuselage.castShadow = true;
        enemy.add(fuselage);

        // 敌机机翼
        const wingGeometry = new THREE.BoxGeometry(8, 0.2, 2);
        const wingMaterial = new THREE.MeshPhongMaterial({
            color: 0x8B0000,
            shininess: 80
        });
        const wings = new THREE.Mesh(wingGeometry, wingMaterial);
        wings.position.z = -0.5;
        wings.castShadow = true;
        enemy.add(wings);

        // 敌机尾翼
        const tailGeometry = new THREE.BoxGeometry(2, 0.15, 1);
        const tail = new THREE.Mesh(tailGeometry, wingMaterial);
        tail.position.x = 2.5;
        tail.castShadow = true;
        enemy.add(tail);

        // 随机生成位置
        const spawnDistance = 150;
        const angle = Math.random() * Math.PI * 2;
        const height = Math.random() * 40 + 20;

        enemy.position.set(
            Math.cos(angle) * spawnDistance,
            height,
            Math.sin(angle) * spawnDistance
        );

        // 敌机属性
        enemy.userData = {
            speed: 15 + Math.random() * 10,
            health: 1,
            type: 'enemy',
            movePattern: Math.floor(Math.random() * 3),
            time: 0,
            targetPosition: new THREE.Vector3(),
            velocity: new THREE.Vector3()
        };

        // 设置初始朝向（朝向玩家）
        enemy.lookAt(this.player.position);

        this.enemies.push(enemy);
        this.scene.add(enemy);
        this.gameState.enemyCount++;

        return enemy;
    }

    fireMissile() {
        if (!this.player) return;

        const currentTime = this.clock.getElapsedTime();
        if (currentTime - this.lastMissileTime < this.missileCooldown) {
            return;
        }

        this.lastMissileTime = currentTime;

        // 创建导弹
        const missileGeometry = new THREE.CylinderGeometry(0.1, 0.15, 2, 8);
        const missileMaterial = new THREE.MeshPhongMaterial({
            color: 0xFFFF00,
            emissive: 0x444400
        });
        const missileMesh = new THREE.Mesh(missileGeometry, missileMaterial);

        // 设置导弹初始位置和方向
        missileMesh.position.copy(this.player.position);
        missileMesh.position.add(new THREE.Vector3(4, 0, 0));

        // 获取玩家朝向
        const direction = new THREE.Vector3();
        this.player.getWorldDirection(direction);

        const missile = {
            mesh: missileMesh,
            velocity: direction.multiplyScalar(60),
            life: 5.0,
            trail: []
        };

        this.missiles.push(missile);
        this.scene.add(missileMesh);

        // 播放音效
        this.audioManager.playShoot();
    }

    startGame() {
        console.log('开始3D空战');

        // 启动音频上下文
        this.audioManager.resumeAudioContext();

        document.getElementById('startScreen').style.display = 'none';
        this.gameState.isGameRunning = true;
        this.gameState.updateUI();
        this.clock.start();

        // 显示游戏提示
        setTimeout(() => {
            console.log('🎮 3D空战控制提示：');
            console.log('- WASD: 控制飞机移动');
            console.log('- 鼠标: 控制视角和飞机朝向');
            console.log('- 空格: 发射导弹');
            console.log('- Shift: 加速飞行');
            console.log('- 点击画面锁定鼠标控制');
        }, 1000);
    }

    restartGame() {
        document.getElementById('gameOverScreen').style.display = 'none';
        this.gameState.reset();
        this.gameState.isGameRunning = true;
        this.gameState.updateUI();

        // 清理游戏对象
        this.clearGameObjects();

        // 重置玩家位置
        this.player.position.set(0, 10, 0);
        this.player.rotation.set(0, 0, 0);
    }

    clearGameObjects() {
        // 清理导弹
        this.missiles.forEach(missile => this.scene.remove(missile.mesh));
        this.missiles = [];

        // 清理敌机
        this.enemies.forEach(enemy => this.scene.remove(enemy));
        this.enemies = [];

        // 清理爆炸效果
        this.explosions.forEach(explosion => this.scene.remove(explosion.mesh));
        this.explosions = [];

        // 重置计数
        this.gameState.enemyCount = 0;
    }

    gameOver() {
        this.gameState.isGameRunning = false;
        document.getElementById('finalScore').textContent = `最终得分: ${this.gameState.score}`;
        document.getElementById('gameOverScreen').style.display = 'flex';

        // 退出指针锁定
        if (document.pointerLockElement) {
            document.exitPointerLock();
        }
    }

    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }

    update(deltaTime) {
        if (!this.gameState.isGameRunning || this.gameState.isPaused) {
            return;
        }

        // 处理输入
        this.handleInput(deltaTime);

        // 更新游戏对象
        this.updatePlayer(deltaTime);
        this.updateMissiles(deltaTime);
        this.updateEnemies(deltaTime);
        this.updateExplosions(deltaTime);
        this.updateCamera(deltaTime);

        // 检测碰撞
        this.checkCollisions();

        // 生成敌机
        this.spawnEnemies();

        // 更新云朵动画
        if (this.cloudGroup) {
            this.cloudGroup.rotation.y += deltaTime * 0.01;
        }

        // 更新UI
        this.gameState.updateUI();
        this.updateFPS();
    }

    handleInput(deltaTime) {
        // ESC键暂停
        if (this.inputManager.keys.escape) {
            this.gameState.isPaused = !this.gameState.isPaused;
            this.inputManager.keys.escape = false;

            const pauseScreen = document.getElementById('pauseScreen');
            pauseScreen.style.display = this.gameState.isPaused ? 'flex' : 'none';

            if (this.gameState.isPaused && document.pointerLockElement) {
                document.exitPointerLock();
            }
        }

        if (!this.player) return;

        const speed = this.player.userData.speed * deltaTime;
        const boostMultiplier = this.inputManager.keys.shift ? 2 : 1;
        const finalSpeed = speed * boostMultiplier;

        // WASD移动控制
        const moveVector = new THREE.Vector3();

        if (this.inputManager.keys.w) {
            moveVector.z -= finalSpeed;
        }
        if (this.inputManager.keys.s) {
            moveVector.z += finalSpeed;
        }
        if (this.inputManager.keys.a) {
            moveVector.x -= finalSpeed;
        }
        if (this.inputManager.keys.d) {
            moveVector.x += finalSpeed;
        }

        // 应用移动
        this.player.position.add(moveVector);

        // 鼠标控制飞机旋转
        if (document.pointerLockElement) {
            const rotationSpeed = 0.002;
            this.player.rotation.y -= this.inputManager.mouse.deltaX * rotationSpeed;
            this.player.rotation.x -= this.inputManager.mouse.deltaY * rotationSpeed;

            // 限制俯仰角度
            this.player.rotation.x = Math.max(-Math.PI/3, Math.min(Math.PI/3, this.player.rotation.x));
        }

        // 限制玩家在边界内
        this.player.position.x = Math.max(-this.worldBounds.x, Math.min(this.worldBounds.x, this.player.position.x));
        this.player.position.y = Math.max(5, Math.min(this.worldBounds.y, this.player.position.y));
        this.player.position.z = Math.max(-this.worldBounds.z, Math.min(this.worldBounds.z, this.player.position.z));

        // 空格键发射导弹
        if (this.inputManager.keys.space) {
            this.fireMissile();
        }

        // 重置鼠标增量
        this.inputManager.mouse.deltaX = 0;
        this.inputManager.mouse.deltaY = 0;
    }

    updatePlayer(deltaTime) {
        if (!this.player) return;

        // 飞机倾斜效果
        const targetRoll = 0;
        if (this.inputManager.keys.a) {
            this.player.rotation.z = Math.min(this.player.rotation.z + deltaTime * 2, Math.PI / 6);
        } else if (this.inputManager.keys.d) {
            this.player.rotation.z = Math.max(this.player.rotation.z - deltaTime * 2, -Math.PI / 6);
        } else {
            // 回正
            if (this.player.rotation.z > 0) {
                this.player.rotation.z = Math.max(this.player.rotation.z - deltaTime * 3, 0);
            } else if (this.player.rotation.z < 0) {
                this.player.rotation.z = Math.min(this.player.rotation.z + deltaTime * 3, 0);
            }
        }
    }

    updateMissiles(deltaTime) {
        for (let i = this.missiles.length - 1; i >= 0; i--) {
            const missile = this.missiles[i];

            // 更新导弹位置
            missile.mesh.position.add(missile.velocity.clone().multiplyScalar(deltaTime));

            // 导弹朝向飞行方向
            missile.mesh.lookAt(missile.mesh.position.clone().add(missile.velocity));

            // 减少生命周期
            missile.life -= deltaTime;

            // 检查边界和生命周期
            if (missile.life <= 0 ||
                Math.abs(missile.mesh.position.x) > this.worldBounds.x ||
                Math.abs(missile.mesh.position.y) > this.worldBounds.y ||
                Math.abs(missile.mesh.position.z) > this.worldBounds.z) {

                this.scene.remove(missile.mesh);
                this.missiles.splice(i, 1);
            }
        }
    }

    updateEnemies(deltaTime) {
        for (let i = this.enemies.length - 1; i >= 0; i--) {
            const enemy = this.enemies[i];
            enemy.userData.time += deltaTime;

            // 简单AI：朝向玩家移动
            const directionToPlayer = new THREE.Vector3()
                .subVectors(this.player.position, enemy.position)
                .normalize();

            // 根据移动模式更新位置
            switch (enemy.userData.movePattern) {
                case 0: // 直接追击
                    enemy.position.add(directionToPlayer.multiplyScalar(enemy.userData.speed * deltaTime));
                    break;

                case 1: // 螺旋接近
                    const spiralOffset = new THREE.Vector3(
                        Math.sin(enemy.userData.time * 2) * 5,
                        Math.cos(enemy.userData.time * 3) * 3,
                        Math.cos(enemy.userData.time * 2) * 5
                    );
                    enemy.position.add(directionToPlayer.multiplyScalar(enemy.userData.speed * deltaTime * 0.7));
                    enemy.position.add(spiralOffset.multiplyScalar(deltaTime));
                    break;

                case 2: // 侧向攻击
                    const sideVector = new THREE.Vector3()
                        .crossVectors(directionToPlayer, new THREE.Vector3(0, 1, 0))
                        .normalize();
                    const sideOffset = sideVector.multiplyScalar(Math.sin(enemy.userData.time) * 10);
                    enemy.position.add(directionToPlayer.multiplyScalar(enemy.userData.speed * deltaTime * 0.5));
                    enemy.position.add(sideOffset.multiplyScalar(deltaTime));
                    break;
            }

            // 敌机朝向玩家
            enemy.lookAt(this.player.position);

            // 检查是否距离玩家太远
            const distanceToPlayer = enemy.position.distanceTo(this.player.position);
            if (distanceToPlayer > 300) {
                this.scene.remove(enemy);
                this.enemies.splice(i, 1);
                this.gameState.enemyCount--;
            }
        }
    }

    updateExplosions(deltaTime) {
        for (let i = this.explosions.length - 1; i >= 0; i--) {
            const explosion = this.explosions[i];

            explosion.life -= deltaTime;

            // 更新爆炸效果
            const scale = 1 + (1 - explosion.life / explosion.maxLife) * 3;
            explosion.mesh.scale.setScalar(scale);

            // 透明度变化
            explosion.mesh.material.opacity = explosion.life / explosion.maxLife;

            if (explosion.life <= 0) {
                this.scene.remove(explosion.mesh);
                this.explosions.splice(i, 1);
            }
        }
    }

    updateCamera(deltaTime) {
        if (this.controls) {
            this.controls.update();

            // 相机跟随玩家
            const targetPosition = this.player.position.clone();
            targetPosition.add(new THREE.Vector3(-20, 10, 0));

            this.camera.position.lerp(targetPosition, deltaTime * 2);
            this.controls.target.lerp(this.player.position, deltaTime * 3);
        }
    }

    checkCollisions() {
        // 检测导弹与敌机的碰撞
        for (let i = this.missiles.length - 1; i >= 0; i--) {
            const missile = this.missiles[i];

            for (let j = this.enemies.length - 1; j >= 0; j--) {
                const enemy = this.enemies[j];

                const distance = missile.mesh.position.distanceTo(enemy.position);

                if (distance < 5) {
                    // 创建爆炸效果
                    this.createExplosion(enemy.position.clone());

                    // 播放爆炸音效
                    this.audioManager.playExplosion();

                    // 移除导弹和敌机
                    this.scene.remove(missile.mesh);
                    this.missiles.splice(i, 1);

                    this.scene.remove(enemy);
                    this.enemies.splice(j, 1);
                    this.gameState.enemyCount--;

                    // 增加得分
                    this.gameState.score += 10;

                    break;
                }
            }
        }

        // 检测玩家与敌机的碰撞
        if (this.player) {
            for (let i = this.enemies.length - 1; i >= 0; i--) {
                const enemy = this.enemies[i];
                const distance = this.player.position.distanceTo(enemy.position);

                if (distance < 8) {
                    // 创建爆炸效果
                    this.createExplosion(enemy.position.clone());

                    // 播放受击音效
                    this.audioManager.playHit();

                    // 移除敌机
                    this.scene.remove(enemy);
                    this.enemies.splice(i, 1);
                    this.gameState.enemyCount--;

                    // 玩家失去生命
                    this.gameState.lives--;

                    if (this.gameState.lives <= 0) {
                        this.gameOver();
                    }

                    break;
                }
            }
        }
    }

    createExplosion(position) {
        // 创建3D爆炸效果
        const explosionGeometry = new THREE.SphereGeometry(2, 8, 6);
        const explosionMaterial = new THREE.MeshBasicMaterial({
            color: 0xFF4500,
            transparent: true,
            opacity: 0.8
        });

        const explosionMesh = new THREE.Mesh(explosionGeometry, explosionMaterial);
        explosionMesh.position.copy(position);

        const explosion = {
            mesh: explosionMesh,
            life: 1.0,
            maxLife: 1.0
        };

        this.explosions.push(explosion);
        this.scene.add(explosionMesh);
    }

    spawnEnemies() {
        if (!this.lastEnemySpawnTime) {
            this.lastEnemySpawnTime = 0;
        }

        const currentTime = this.clock.getElapsedTime();
        const spawnInterval = Math.max(3.0 - this.gameState.level * 0.3, 1.0);
        const maxEnemies = 5 + this.gameState.level;

        if (currentTime - this.lastEnemySpawnTime > spawnInterval &&
            this.enemies.length < maxEnemies) {
            this.lastEnemySpawnTime = currentTime;
            this.createEnemy();
        }

        // 检查等级提升
        if (this.gameState.score > 0 && this.gameState.score % 100 === 0 &&
            this.gameState.score !== this.lastLevelScore) {
            this.gameState.level++;
            this.lastLevelScore = this.gameState.score;
            console.log(`🎉 等级提升到 ${this.gameState.level}！`);
        }
    }

    updateFPS() {
        this.frameCount++;
        const currentTime = this.clock.getElapsedTime();

        if (currentTime - this.lastFPSUpdate >= 1.0) {
            const fps = Math.round(this.frameCount / (currentTime - this.lastFPSUpdate));
            document.getElementById('fps').textContent = fps;
            this.frameCount = 0;
            this.lastFPSUpdate = currentTime;
        }
    }

    startGameLoop() {
        const animate = () => {
            requestAnimationFrame(animate);

            if (this.gameState.isLoaded) {
                const deltaTime = this.clock.getDelta();
                this.update(deltaTime);
                this.renderer.render(this.scene, this.camera);
            }
        };

        animate();
    }
}

// 启动游戏
window.addEventListener('DOMContentLoaded', () => {
    console.log('启动3D空战游戏...');

    try {
        const game = new AirCombat3D();
        window.game = game; // 用于调试
        console.log('3D空战游戏启动成功！');
    } catch (error) {
        console.error('游戏启动失败:', error);
        document.getElementById('loadingStatus').textContent = '启动失败: ' + error.message;
        alert('游戏启动失败: ' + error.message);
    }
});
